# 表名修改说明：order_es_processing_failed → canal_processing_failed

## 概述
将数据库表 `order_es_processing_failed` 改名为 `canal_processing_failed`，并更新相关类的注释和文档说明。

## 修改内容

### 1. 实体类修改
**文件**: `hengjian-business/zsmall-order/zsmall-order-entity/src/main/java/com/zsmall/order/entity/domain/OrderEsProcessingFailed.java`

修改内容：
- 更新 `@TableName` 注解：`"order_es_processing_failed"` → `"canal_processing_failed"`
- 更新类注释：`订单ES消息处理失败对象` → `Canal消息处理失败对象`

### 2. 视图对象修改
**文件**: `hengjian-business/zsmall-order/zsmall-order-entity/src/main/java/com/zsmall/order/entity/domain/vo/order/OrderEsProcessingFailedVo.java`

修改内容：
- 更新类注释：`订单ES消息处理失败视图对象` → `Canal消息处理失败视图对象`

### 3. Mapper接口修改
**文件**: `hengjian-business/zsmall-order/zsmall-order-entity/src/main/java/com/zsmall/order/entity/mapper/OrderEsProcessingFailedMapper.java`

修改内容：
- 更新接口注释：`订单ES消息处理失败Mapper接口` → `Canal消息处理失败Mapper接口`

### 4. 业务代码注释更新

#### CanalOrderJob.java
**文件**: `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/job/orderEs/CanalOrderJob.java`

修改内容：
- 第103行：`查询订单ES消息处理失败表` → `查询Canal消息处理失败表`
- 第157行：`订单ES消息处理失败表处理失败` → `Canal消息处理失败表处理失败`

#### CanalOrderRabbitMQListener.java
**文件**: `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/mq/CanalOrderRabbitMQListener.java`

修改内容：
- 第60行：`记录进ES异常表` → `记录进Canal处理失败表`

#### CanalProductSkuStockRabbitMQListener.java
**文件**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/mq/CanalProductSkuStockRabbitMQListener.java`

修改内容：
- 第60行：`记录进ES异常表` → `记录进Canal处理失败表`

### 5. 文档更新
**文件**: `docs/CanalProductSkuStockRabbitMQListener_创建说明.md`

修改内容：
- 第79行：添加说明表名已更改为 `canal_processing_failed`
- 第94行：`order_es_processing_failed` → `canal_processing_failed`

## 数据库变更

需要执行以下SQL语句来重命名数据库表：

```sql
-- 重命名表
ALTER TABLE order_es_processing_failed RENAME TO canal_processing_failed;
```

## 影响范围

### 保持不变的内容
- 类名：`OrderEsProcessingFailed`、`OrderEsProcessingFailedVo`、`OrderEsProcessingFailedMapper` 保持不变
- 字段结构：表字段完全不变
- 业务逻辑：所有业务处理逻辑保持不变
- 接口调用：所有现有的接口调用方式保持不变

### 变更的内容
- 数据库表名：`order_es_processing_failed` → `canal_processing_failed`
- 相关注释和文档说明更新为更通用的Canal处理失败表

## 兼容性说明

1. **向后兼容**：由于只修改了表名和注释，现有代码无需修改即可正常工作
2. **数据迁移**：通过重命名表的方式，现有数据完全保留
3. **功能扩展**：新的表名更好地反映了其通用性，可以用于记录所有Canal相关的处理失败消息

## 注意事项

1. 在生产环境执行表重命名操作前，请确保：
   - 备份相关数据
   - 在维护窗口期间执行
   - 验证应用程序连接正常

2. 如果有其他系统或脚本直接引用了旧表名，需要同步更新

3. 监控和日志系统中如果有硬编码的表名，也需要相应更新
