package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品库存处理失败记录表
 *
 * <AUTHOR>
 */
@Data
@TableName("product_sku_stock_processing_failed")
public class ProductSkuStockProcessingFailed implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId
    private Long id;
    
    /**
     * 数据库名
     */
    private String databaseName;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 操作类型
     */
    private String type;
    
    /**
     * 处理次数
     */
    private Integer processingNum;
    
    /**
     * 处理状态 0-失败 1-成功
     */
    private Integer processingStatus;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * Channel对象JSON
     */
    private String channelObject;
    
    /**
     * Message对象JSON
     */
    private String messageObject;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
