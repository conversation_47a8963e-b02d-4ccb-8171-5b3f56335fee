package com.zsmall.product.entity.domain.mq;

import lombok.Data;

import java.io.Serializable;

/**
 * Canal产品库存数据实体
 *
 * <AUTHOR>
 */
@Data
public class CanalProductSkuStock implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 库存编号
     */
    private String stockCode;
    
    /**
     * 商品SKU编号
     */
    private String productSkuCode;
    
    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;
    
    /**
     * 总库存
     */
    private Integer stockTotal;
    
    /**
     * 预留库存
     */
    private Integer stockReserved;
    
    /**
     * 自提可用库存
     */
    private Integer stockAvailable;
    
    /**
     * 代发可用库存标识 0单仓,1非单仓(等于自提库存)
     */
    private Integer dropShippingStockAvailable;
    
    /**
     * 库存状态（0-停用，1-启用等）
     */
    private Integer stockState;
    
    /**
     * 自提锁定已使用
     */
    private Integer pickupLockUsed;
    
    /**
     * 自提锁定预留
     */
    private Integer pickupLockReserved;
    
    /**
     * 代发锁定已使用
     */
    private Integer dropShippingLockUsed;
    
    /**
     * 代发锁定预留
     */
    private Integer dropShippingLockReserved;
    
    /**
     * 锁定异常代码
     */
    private String lockExceptionCode;
    
    /**
     * 关联物流模板编号
     */
    private String logisticsTemplateNo;
    
    /**
     * 租户编号
     */
    private String tenantId;
    
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;
    
    /**
     * 创建者
     */
    private Long createBy;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新者
     */
    private Long updateBy;
    
    /**
     * 更新时间
     */
    private String updateTime;
}
