package com.zsmall.product.biz.support;

import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.rabbitmq.client.Channel;
import com.zsmall.product.entity.domain.mq.CanalProductSkuStockMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Canal产品库存处理支持类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CanalProductSkuStockSupport {
    
    @Resource
    private Redisson redisson;
    
    private static final String DEAL_MESSAGE_LOCK_KEY = GlobalConstants.GLOBAL_REDIS_KEY + "canal:product:sku:stock:deal:message:lock";
    
    /**
     * 处理产品库存变更MQ消息
     * @param message 消息
     * @param channel channel
     * @param isJob 是否来自定时任务
     */
    public void dealCanalProductSkuStockMqMessage(Message message, Channel channel, Boolean isJob) throws Exception {
        String messageContext = new String(message.getBody());
        if (ObjectUtil.isNull(messageContext) || ObjectUtil.isNull(channel) || StrUtil.isEmpty(messageContext)) {
            log.warn("Canal产品库存变更消息为空或Channel为空");
            return;
        }
        
        CanalProductSkuStockMqDTO canalProductSkuStockDTO = JSON.parseObject(messageContext, CanalProductSkuStockMqDTO.class);
        log.info("接收到Canal产品库存变更信息,来源：{}，表名:{},操作类型：{}，消息内容:{}", 
                isJob ? "xxl-job" : "MQ", canalProductSkuStockDTO.getTable(), canalProductSkuStockDTO.getType(), messageContext);
        
        int hash = HashUtil.apHash(messageContext);
        if (!isJob) {
            boolean existsObject = RedisUtils.isExistsObject(GlobalConstants.GLOBAL_REDIS_KEY + hash);
            if (existsObject) {
                log.error("HASH值已存在{}", hash);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }
        } else {
            RedisUtils.setCacheObject(GlobalConstants.GLOBAL_REDIS_KEY + hash, hash);
            RedisUtils.expire(GlobalConstants.GLOBAL_REDIS_KEY + hash, 60 * 60);
        }
        
        log.info("{}业务开始执行 表名:{} 消息体为:{}", canalProductSkuStockDTO.getType(), canalProductSkuStockDTO.getTable(), messageContext);
        dealCanalProductSkuStockMqMessageData(canalProductSkuStockDTO, messageContext);
        log.info("{}业务执行完成 表名:{} 消息体为:{}", canalProductSkuStockDTO.getType(), canalProductSkuStockDTO.getTable(), messageContext);
    }
    
    /**
     * 处理产品库存变更MQ消息数据
     * @param canalProductSkuStockDTO 消息DTO
     * @param messageContext 消息内容
     * @throws Exception 异常
     */
    private void dealCanalProductSkuStockMqMessageData(CanalProductSkuStockMqDTO canalProductSkuStockDTO, String messageContext) throws Exception {
        RLock lock = redisson.getLock(DEAL_MESSAGE_LOCK_KEY);
        try {
            // 尝试获取锁，等待3秒，10秒后自动释放
            if (!lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                log.warn("获取产品库存消息处理锁失败，消息将被丢弃或重试: {}", canalProductSkuStockDTO);
                return;
            }
            
            String tableName = canalProductSkuStockDTO.getTable();
            String type = canalProductSkuStockDTO.getType();
            if (StrUtil.isEmpty(tableName) || StrUtil.isEmpty(type)) {
                return;
            }
            
            // TODO: 根据操作类型处理不同的业务逻辑
            switch (type) {
                case "INSERT":
                    processInsertFields(canalProductSkuStockDTO, tableName);
                    break;
                case "UPDATE":
                    processUpdateFields(canalProductSkuStockDTO, tableName);
                    break;
                case "DELETE":
                    processDeleteFields(canalProductSkuStockDTO, tableName);
                    break;
                default:
                    log.warn("未知的操作类型: {}", type);
                    break;
            }
        } finally {
            // 确保锁一定被释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 处理插入操作
     * @param canalProductSkuStockDTO 消息DTO
     * @param tableName 表名
     */
    private void processInsertFields(CanalProductSkuStockMqDTO canalProductSkuStockDTO, String tableName) {
        try {
            log.info("处理产品库存插入操作，表名：{}，数据：{}", tableName, canalProductSkuStockDTO.getData());
            // TODO: 实现具体的插入处理逻辑
        } catch (Exception e) {
            log.error("[Canal产品库存新增业务异常]{}", e.getMessage(), e);
            throw new RuntimeException("[Canal产品库存新增业务异常]" + e.getMessage(), e);
        }
    }
    
    /**
     * 处理更新操作
     * @param canalProductSkuStockDTO 消息DTO
     * @param tableName 表名
     */
    private void processUpdateFields(CanalProductSkuStockMqDTO canalProductSkuStockDTO, String tableName) {
        try {
            log.info("处理产品库存更新操作，表名：{}，新数据：{}，旧数据：{}", tableName, canalProductSkuStockDTO.getData(), canalProductSkuStockDTO.getOld());
            // TODO: 实现具体的更新处理逻辑
        } catch (Exception e) {
            log.error("[Canal产品库存更新业务异常]{}", e.getMessage(), e);
            throw new RuntimeException("[Canal产品库存更新业务异常]" + e.getMessage(), e);
        }
    }
    
    /**
     * 处理删除操作
     * @param canalProductSkuStockDTO 消息DTO
     * @param tableName 表名
     */
    private void processDeleteFields(CanalProductSkuStockMqDTO canalProductSkuStockDTO, String tableName) {
        try {
            log.info("处理产品库存删除操作，表名：{}，数据：{}", tableName, canalProductSkuStockDTO.getData());
            // TODO: 实现具体的删除处理逻辑
        } catch (Exception e) {
            log.error("[Canal产品库存删除业务异常]{}", e.getMessage(), e);
            throw new RuntimeException("[Canal产品库存删除业务异常]" + e.getMessage(), e);
        }
    }
}
