package com.zsmall.order.entity.domain.vo.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.order.entity.domain.CanalProcessingFailed;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;

/**
 * Canal消息处理失败视图对象 canal_processing_failed
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CanalProcessingFailed.class)
public class CanalProcessingFailedVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 数据库名称
     */
    @ExcelProperty(value = "数据库名称")
    private String databaseName;

    /**
     * 表名
     */
    @ExcelProperty(value = "表名")
    private String tableName;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "操作类型")
    private String type;

    /**
     * message对象
     */
    @ExcelProperty(value = "message对象")
    private String messageObject;

    /**
     * channel对象
     */
    @ExcelProperty(value = "channel对象")
    private String channelObject;

    /**
     * 错误原因
     */
    @ExcelProperty(value = "错误原因")
    private String errorMessage;

    /**
     * 异常处理次数
     */
    @ExcelProperty(value = "异常处理次数")
    private Integer processingNum;

    /**
     * 异常处理状态 0处理失败  1处理成功
     */
    @ExcelProperty(value = "异常处理状态 0处理失败  1处理成功")
    private Integer processingStatus;
}
