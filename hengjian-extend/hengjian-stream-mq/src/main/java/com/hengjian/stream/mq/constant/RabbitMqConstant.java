package com.hengjian.stream.mq.constant;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/12 14:46
 */
public class RabbitMqConstant {
    public static final String STORE_SYNCHRONIZATION_QUEUE = "store_synchronization_queue";
    public static final String STORE_SYNCHRONIZATION_EXCHANGE = "store_synchronization_exchange";
    public static final String STORE_SYNCHRONIZATION_KEY = "store_synchronization_key";

    public static final String OPEN_PAY_KEY = "open_pay_key";

    public static final String OPEN_PAY_QUEUE = "open_pay_queue";
    public static final String OPEN_PAY_EXCHANGE = "open_pay_exchange";

    public static final String SHOP_SYNC_MULTI_QUEUE = "shop_sync_multi_queue";
    public static final String SHOP_SYNC_MULTI_EXCHANGE = "shop_sync_multi_exchange";
    public static final String SHOP_SYNC_MULTI_KEY = "shop_sync_multi_key";

    /**
     * 正现金流队列 direct.e.fin.distribution.system.flow.queue
     */
    public static final String POSITIVE_CASH_FLOW_QUEUE = "direct.e.fin.distribution.system.flow.queue";
    /**
     * 正现金流交换
     */
    public static final String POSITIVE_CASH_FLOW_EXCHANGE = "direct.e.fin";


    /**
     * 交换机
     */
    public static final String MULTICHANNEL_SEND_EXCHANGE = "multichannel_send_exchange";
    /**
     * 店铺信息回传队列
     */
    public static final String TIKTOK_SHOP_QUEUE = "tiktok_shop_queue";
    /**
     * 店铺信息回传key
     */
    public static final String TIKTOK_SHOP_ROUTING_KEY = "tiktok_shop_routing_key";

    /**
     * 订单tracking信息队列
     */
    public static final String ORDER_TRACKING_QUEUE = "order_tracking_queue";
    /**
     * 订单tracking信息 key
     */
    public static final String ORDER_TRACKING_ROUTING_KEY = "order_tracking_routing_key";

    /**
     * 订单tracking上传失败队列
     */
    public static final String TRACKING_NOTIFY_ERROR_QUEUE = "tracking.notify.error.queue";

    /**
     * temu订单信息
     */
    public static final String TEMU_ORDER_INFO_QUEUE = "temu.order.info.queue";
    /**
     * temu订单信息 路由
     */
    public static final String TEMU_ORDER_INFO_ROUTING_KEY = "temu.order.info.routing.key";
    /**
     * 正现金流路由密钥
     */
    public static final String POSITIVE_CASH_FLOW_ROUTING_KEY = "direct.e.fin.distribution.system.flow.key";
    /**
     * temu订单信息 交换机
     */
    public static final String TEMU_ORDER_INFO_EXCHANGE = "temu.order.info.exchange";
    public static final String TEMU_ORDER_INFO = "temu.order.info";

    /**
     * 商品映射消息发送队列
     */
    public static final String PRODUCT_MAPPING_REQUEST = "distribution.product.mapping.request.queue";
    /**
     * 商品映射消息接受队列
     */
    public static final String PRODUCT_MAPPING_RESPONSE = "distribution.product.mapping.response.queue";

    /**
     * amazonVC订单物流信息上传队列
     */
    public static final String ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_QUEUE = "order.amazon.vc.logistics.attachment.queue";

    /**
     * amazonVC订单物流信息上传路由
     */
    public static final String ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_ROUTING_KEY = "order.amazon.vc.logistics.attachment.routing.key";

    /**
     * amazonVC订单物流信息回调队列
     */
    public static final String ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_CALL_BACK_QUEUE = "order.amazon.vc.logistics.attachment.call.back.queue";
    /**
     * amazonVC订单物流信息回调路由
     */
    public static final String ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_CALL_ROUTING_KEY = "order.amazon.vc.logistics.attachment.call.back.queue";

    /**
     * 分销订单交换机
     */
    public static final String DISTRIBUTION_ORDER_EXCHANGE = "distribution.order.exchange";

    /**
     * 推送订单路由
     */
    public static final String DISTRIBUTION_PUSH_ORDER_ROUTING_KEY = "distribution.order.routing.key";

    /**
     * 推送订单队列
     */
    public static final String DISTRIBUTION_PUSH_ORDER_QUEUE = "distribution.order.queue";

    /**
     * 推送订单路由
     */
    public static final String DISTRIBUTION_PUSH_ORDER_PAY_ROUTING_KEY = "distribution.order.pay.routing.key";

    /**
     * 推送订单队列
     */
    public static final String DISTRIBUTION_PUSH_ORDER_PAY_QUEUE = "distribution.order.pay.queue";

    /**
     * 账单确认推送ERP队列
     */
    public static final String BILL_CONFIRMED_SEND_ERP_REQUEST = "bill.confirmed.send.erp.request.queue";

    /**
     * 账单修补差值推送ERP队列
     */
    public static final String BILL_REPAIR_SEND_ERP_REQUEST_QUEUE = "bill.repair.send.erp.request.queue";
    /**
     * canal订单交换机
     */
    public static final String CANAL_ORDER_EXCHANGE = "canal_order_exchange";
    /**
     * canal订单Key
     */
    public static final String CANAL_KEY = "canal_order_key";
    /**
     * canal订单队列
     */
    public static final String CANAL_ORDER_QUEUE = "canal_order_queue";

    /**
     * canal产品库存交换机
     */
    public static final String CANAL_PRODUCT_SKU_STOCK_EXCHANGE = "canal_product_sku_stock_exchange";
    /**
     * canal产品库存Key
     */
    public static final String CANAL_PRODUCT_SKU_STOCK_KEY = "canal_product_sku_stock_key";
    /**
     * canal产品库存队列
     */
    public static final String CANAL_PRODUCT_SKU_STOCK_QUEUE = "canal_product_sku_stock_queue";

    /**
     * 产品ES信息同步队列
     */
    public static final String ES_PRODUCT_SYNCHRONIZATION_QUEUE = "es_product_synchronization.queue";

    // ==================== 活动到期相关（延迟消息插件方案） ====================
    /**
     * 活动过期处理队列（实际消费队列）
     */
    public static final String ACTIVITY_EXPIRE_PROCESS_QUEUE = "activity.expire.process.queue";

    /**
     * 活动过期延迟交换机（基于 rabbitmq-delayed-message-exchange 插件）
     */
    public static final String ACTIVITY_EXPIRE_DELAY_EXCHANGE = "activity.expire.delay.exchange";

    /**
     * 活动过期路由键
     */
    public static final String ACTIVITY_EXPIRE_ROUTING_KEY = "activity.expire";

//
    public static final String REVIEW_COMPANY_INFORMATION_QUEUE = "review.company.information.queue";
    public static final String REVIEW_COMPANY_INFORMATION_EXCHANGE = "review.company.information.exchange";
    public static final String REVIEW_COMPANY_INFORMATION_KEY = "review.company.information.key";
}
